defmodule Alldoq.Repo.Migrations.ChangeRegsiterSchema do
  use Ecto.Migration

  def change do
    alter table(:register_experts) do
      remove :expertise, :string
      remove :expertise_details, :string
      add :is_retired, :boolean, default: false
    end

    alter table(:register_regulatory_info) do
      add :reference_expiry_date, :date
      add :ico_expiry_date, :date
    end

    alter table(:register_private_appointments) do
      add :job_title, :string
    end

    alter table(:register_nhs_appointments) do
      add :job_title, :string
    end

    alter table(:register_practice_locations) do
      add :text_address, :string
      remove :address, :string
    end
  end
end
