defmodule Alldoq.Saas.SaasRegistrar do

  use AlldoqWeb, :live_view

  alias Alldoq.{  Saas, Accounts }
  alias Alldoq.Saas.Seeds.TenantSeeder
  alias Alldoq.Accounts.Services.UserPostCreationActions
  alias Alldoq.Saas.{ PlanSettings, RadiologyServer }
  require Logger

  def create_tenant(tenant) do
    Saas.create_tenant(tenant)
  end

  def call(params, tenant) do
    create_tenant(tenant)

    multi =
      Ecto.Multi.new()
      |> Ecto.Multi.run(:account, fn _, _ ->
        params_with_settings = Map.put(params, "plan_settings", PlanSettings.default())
        Saas.create_account(params_with_settings)
      end)
      |> Ecto.Multi.run(:user, fn _, %{account: account} ->
        role = Accounts.first_role()
        data = Map.put(params, "role_id", Map.get(role || %{}, :id))
        Accounts.register_user(data, account)
      end)
      |> post_user_multi(tenant)

    multi
    |> Alldoq.Repo.transaction()
    |> case do
      {:ok, %{user: user, account: account}} ->
        original_password = user.password

        case UserPostCreationActions.call(user) do
          {:ok, updated_user} ->
            updated_user_with_password = Map.put(updated_user, :password, original_password)

            Task.start(fn ->
                  Accounts.deliver_user_confirmation_instructions(
                    updated_user_with_password,
                    &url(~p"/users/confirm/#{&1}")
                  )
            end)
            %{user: updated_user, account: account}
        end
      {:error, _failed_operation, changeset, _changes} ->
        {:error, changeset}
      error ->
        Logger.error("Error creating tenant: #{inspect(error)}")
        {:error, :fail}
    end
  end

  def post_user_multi(multi, tenant) do
    multi
    |> Ecto.Multi.run(:radiology_server, fn _, %{account: account} ->
      Saas.add_radiology_server(account, RadiologyServer.first_server())
    end)
    |> Ecto.Multi.run(:seed_tenant, fn _, _ ->
      case TenantSeeder.call(tenant) do
        {:error, reason} -> {:error, reason}
        _ -> {:ok, tenant}
      end
    end)
  end

  def default_domain, do: System.fetch_env!("DEFAULT_CUSTOMERS_DOMAIN")

end
params = %{"first_name" => "ALLDOQ", "last_name" => "Admin",
"password" => "Lodowka1234#","tenant" => "collaboraslegal", "role_id" => 1,
"name" => "ALLDOQ", "phone_number" => "+************", "domain" => System.fetch_env!("DEFAULT_CUSTOMERS_DOMAIN"),
"email" => "<EMAIL>",# "roles"=> ["super_admin"]
}
